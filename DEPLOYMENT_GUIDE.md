# Complete SaaS Deployment Guide

This guide walks you through deploying the complete Gcandle SaaS product from frontend to backend.

## 🏗 Architecture Overview

```
Frontend (Next.js) → API Gateway → Lambda Functions → DynamoDB + Stripe
     ↓                    ↓              ↓              ↓
   S3 Static         Authentication   Subscription    Database
   Website           (AWS Cognito)    Management      Storage
```

## 📋 Prerequisites

1. **AWS Account** with appropriate permissions
2. **Stripe Account** (test mode for development)
3. **Node.js** 18+ and npm
4. **AWS CLI** configured
5. **Domain name** (optional, for custom domains)

## 🚀 Step 1: Deploy Lambda Functions

### 1.1 Configure Lambda Environment

```bash
cd lambda
cp .env.example .env
```

Edit `.env` with your actual values:
- `STRIPE_SECRET_KEY`: From Stripe Dashboard > Developers > API keys
- `STRIPE_WEBHOOK_SECRET`: From Stripe Dashboard > Developers > Webhooks
- `COGNITO_USER_POOL_ID`: From AWS Cognito Console
- `STRIPE_*_PRICE_ID`: From Stripe Dashboard > Products

### 1.2 Deploy Lambda Functions

```bash
cd lambda
npm install
npm run deploy
```

**Important**: Note the API Gateway URL from the deployment output. You'll need this for the frontend.

### 1.3 Configure Stripe Webhooks

1. Go to Stripe Dashboard > Developers > Webhooks
2. Add endpoint: `https://your-api-gateway-url/prod/stripe/webhook`
3. Select events:
   - `checkout.session.completed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`

## 🎨 Step 2: Configure AWS Cognito

### 2.1 Create User Pool

1. Go to AWS Cognito Console
2. Create User Pool with these settings:
   - **Sign-in options**: Email
   - **Password policy**: Default
   - **MFA**: Optional
   - **User account recovery**: Email only

### 2.2 Create App Client

1. In your User Pool, go to App integration > App clients
2. Create app client:
   - **App type**: Public client
   - **App client name**: gcandle-frontend
   - **Authentication flows**: ALLOW_USER_SRP_AUTH
   - **OAuth 2.0 settings**:
     - **Allowed callback URLs**: 
       - `http://localhost:9001/auth/callback` (development)
       - `https://your-domain.com/auth/callback` (production)
     - **Allowed sign-out URLs**:
       - `http://localhost:9001` (development)
       - `https://your-domain.com` (production)
     - **OAuth grant types**: Authorization code grant
     - **OAuth scopes**: email, openid, profile

### 2.3 Configure Hosted UI Domain

1. Go to App integration > Domain
2. Create Cognito domain or use custom domain
3. Note the domain URL for frontend configuration

## 🌐 Step 3: Configure Frontend

### 3.1 Environment Configuration

```bash
cd frontend
cp .env.example .env.local
```

Edit `.env.local` with your actual values:

```env
# AWS Cognito
NEXT_PUBLIC_AWS_REGION=your-aws-region
NEXT_PUBLIC_COGNITO_USER_POOL_ID=your-user-pool-id
NEXT_PUBLIC_COGNITO_CLIENT_ID=your-app-client-id
NEXT_PUBLIC_COGNITO_DOMAIN=your-cognito-domain

# API Gateway
NEXT_PUBLIC_API_ENDPOINT=https://your-api-gateway-url/prod

# Stripe
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key
NEXT_PUBLIC_STRIPE_PROFESSIONAL_MONTHLY_PRICE_ID=price_xxxxx
NEXT_PUBLIC_STRIPE_PROFESSIONAL_ANNUAL_PRICE_ID=price_xxxxx
NEXT_PUBLIC_STRIPE_ENTERPRISE_MONTHLY_PRICE_ID=price_xxxxx
NEXT_PUBLIC_STRIPE_ENTERPRISE_ANNUAL_PRICE_ID=price_xxxxx

# Redirect URLs
NEXT_PUBLIC_REDIRECT_SIGN_IN=http://localhost:9001/auth/callback
NEXT_PUBLIC_REDIRECT_SIGN_OUT=http://localhost:9001
```

### 3.2 Install Dependencies and Test Locally

```bash
cd frontend
npm install
npm run dev
```

Visit `http://localhost:9001` to test the application.

### 3.3 Build for Production

```bash
npm run build
```

This creates a static export in the `out/` directory.

## ☁️ Step 4: Deploy to AWS S3

### 4.1 Create S3 Bucket

```bash
aws s3 mb s3://your-bucket-name --region your-region
```

### 4.2 Configure S3 for Static Website Hosting

```bash
aws s3 website s3://your-bucket-name --index-document index.html --error-document 404.html
```

### 4.3 Upload Frontend Files

```bash
cd frontend
aws s3 sync out/ s3://your-bucket-name --delete
```

### 4.4 Configure Bucket Policy

Create a bucket policy to allow public read access:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "PublicReadGetObject",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::your-bucket-name/*"
    }
  ]
}
```

## 🔧 Step 5: Configure CORS

### 5.1 API Gateway CORS

Ensure your API Gateway has CORS enabled for your S3 website URL:

```bash
# Update lambda/.env with your S3 website URL
FRONTEND_URL=https://your-bucket-name.s3-website-your-region.amazonaws.com
```

Redeploy Lambda functions:

```bash
cd lambda
npm run deploy
```

## 🧪 Step 6: Test End-to-End Flow

### 6.1 Test Authentication

1. Visit your S3 website URL
2. Click "Sign In" 
3. Complete Cognito authentication flow
4. Verify you're redirected back to the site

### 6.2 Test Subscription Flow

1. Go to `/plan` page
2. Select Professional plan
3. Complete Stripe checkout (use test card: 4242 4242 4242 4242)
4. Verify subscription appears in `/profile`

### 6.3 Test Usage Tracking

1. Use search functionality (if implemented)
2. Check usage dashboard in profile
3. Verify usage limits are enforced

### 6.4 Test Billing Management

1. Go to profile page
2. Click "Manage Billing"
3. Verify Stripe customer portal opens
4. Test subscription cancellation

## 🔒 Security Checklist

- [ ] Stripe webhook endpoints are secured
- [ ] Cognito redirect URLs are properly configured
- [ ] API Gateway has proper CORS settings
- [ ] S3 bucket has minimal required permissions
- [ ] Environment variables don't contain secrets in frontend
- [ ] Lambda functions have minimal IAM permissions

## 🚨 Troubleshooting

### Common Issues

1. **CORS Errors**: Check API Gateway CORS settings and frontend URL configuration
2. **Authentication Failures**: Verify Cognito redirect URLs match exactly
3. **Payment Issues**: Ensure Stripe webhook endpoints are configured correctly
4. **Build Failures**: Check all required environment variables are set

### Debug Steps

1. Check browser console for errors
2. Check API Gateway logs in CloudWatch
3. Check Lambda function logs in CloudWatch
4. Verify Stripe webhook delivery in Stripe Dashboard

## 📈 Production Considerations

1. **Custom Domain**: Set up CloudFront distribution with custom domain
2. **SSL Certificate**: Use AWS Certificate Manager for HTTPS
3. **Monitoring**: Set up CloudWatch alarms and dashboards
4. **Backup**: Configure DynamoDB backups
5. **Scaling**: Monitor Lambda concurrency and API Gateway throttling

## 🔄 Continuous Deployment

Consider setting up GitHub Actions or AWS CodePipeline for automated deployments:

1. **Frontend**: Automatically sync to S3 on main branch push
2. **Backend**: Automatically deploy Lambda functions on changes
3. **Testing**: Run integration tests before deployment

## ✅ Testing Checklist

### Authentication Flow
- [ ] User can sign up with email
- [ ] User receives verification email
- [ ] User can sign in with verified account
- [ ] User is redirected correctly after sign in
- [ ] User can sign out
- [ ] Protected routes redirect to sign in

### Subscription Management
- [ ] Free plan users can access basic features
- [ ] Professional plan checkout works with test card
- [ ] Enterprise plan redirects to contact sales
- [ ] Subscription status displays correctly in profile
- [ ] Billing portal opens and functions correctly
- [ ] Subscription cancellation works
- [ ] Webhook events are processed correctly

### Usage Tracking
- [ ] Usage dashboard displays current usage
- [ ] Search tracking increments counters
- [ ] API call tracking works
- [ ] Usage limits are enforced
- [ ] Usage resets correctly (daily/monthly)
- [ ] Upgrade prompts appear when approaching limits

### API Integration
- [ ] All API endpoints respond correctly
- [ ] Authentication headers are sent properly
- [ ] Error handling works for failed requests
- [ ] CORS is configured correctly
- [ ] Rate limiting works as expected

## 📞 Support

For issues or questions:
1. Check CloudWatch logs for detailed error messages
2. Review Stripe Dashboard for payment-related issues
3. Check AWS Cognito Console for authentication issues
4. Verify all environment variables are correctly set
