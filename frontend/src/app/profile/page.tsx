'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { TokenManager } from '@/lib/auth/token-manager';
import { SubscriptionCard } from '@/components/subscription/SubscriptionCard';
import { UsageDashboard } from '@/components/subscription/UsageDashboard';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { User, Mail, Shield, Calendar, Settings } from 'lucide-react';

export default function ProfilePage() {
  const { isAuthenticated, isLoading, user, getAccessToken, signOut } = useAuth();
  const router = useRouter();
  const [userInfo, setUserInfo] = useState<{ name?: string; email?: string; sub?: string; email_verified?: boolean } | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // If user is not authenticated and loading is complete, redirect to login page
    if (!isLoading && !isAuthenticated) {
      router.push('/signin?redirect=/profile');
      return;
    }

    // If user is authenticated, fetch user information
    const fetchUserInfo = async () => {
      try {
        setLoading(true);
        // Get user information from authentication context
        if (user) {
          setUserInfo(user);
        } else {
          // If no user info in auth context, try to get from stored user info
          const storedUserInfo = TokenManager.getStoredUserInfo();
          if (storedUserInfo) {
            setUserInfo(storedUserInfo);
          }
        }
      } catch (error) {
        console.error('Failed to fetch user info:', error);
      } finally {
        setLoading(false);
      }
    };

    if (isAuthenticated) {
      fetchUserInfo();
    }
  }, [isAuthenticated, isLoading, router, user, getAccessToken]);

  const handleLogout = async () => {
    await signOut();
    router.push('/');
  };

  if (isLoading || loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-gray-900"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Account Dashboard</h1>
        <p className="text-gray-600">Manage your account settings and subscription</p>
      </div>

      {userInfo ? (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* User Profile Card */}
          <div className="lg:col-span-1 space-y-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="w-5 h-5" />
                  Profile
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center space-x-4">
                  <div className="bg-gradient-to-r from-[#202438] to-[#d82a21] text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">
                    {userInfo.name ? userInfo.name.charAt(0).toUpperCase() : 'U'}
                  </div>
                  <div>
                    <h2 className="text-xl font-semibold">{userInfo.name || 'User'}</h2>
                    <p className="text-gray-600">{userInfo.email || 'No email information'}</p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Mail className="w-4 h-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500">Email</p>
                      <p className="font-medium">{userInfo.email || 'Unknown'}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <Shield className="w-4 h-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500">Verification Status</p>
                      <div className="flex items-center gap-2">
                        {userInfo.email_verified ? (
                          <Badge variant="default" className="bg-green-100 text-green-800">
                            Verified
                          </Badge>
                        ) : (
                          <Badge variant="destructive">
                            Not Verified
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <Calendar className="w-4 h-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500">User ID</p>
                      <p className="font-medium text-xs">{userInfo.sub || 'Unknown'}</p>
                    </div>
                  </div>
                </div>

                <div className="pt-4 border-t">
                  <Button
                    onClick={handleLogout}
                    variant="outline"
                    className="w-full text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <Settings className="w-4 h-4 mr-2" />
                    Sign Out
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Usage Dashboard */}
            <UsageDashboard />
          </div>

          {/* Subscription Management */}
          <div className="lg:col-span-2">
            <SubscriptionCard />
          </div>
        </div>
      ) : (
        <Card>
          <CardContent className="text-center py-8">
            <p className="text-gray-600">Unable to retrieve user information</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}