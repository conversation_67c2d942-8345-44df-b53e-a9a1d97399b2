/**
 * Authentication Callback Page
 *
 * Handles the OAuth callback from the authentication service.
 * This page processes the authentication result and redirects the user appropriately.
 */

"use client";

import { useEffect, useState, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@/hooks/useAuth";
import { handleAuthCallback } from "@/lib/auth/auth-utils";
import { CheckCircle, XCircle, Loader2 } from "lucide-react";

function AuthCallbackContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { refreshAuth } = useAuth();

  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [error, setError] = useState<string | null>(null);
  const [userInfo, setUserInfo] = useState<{ name?: string; email?: string } | null>(null);

  useEffect(() => {
    let mounted = true;

    async function processCallback() {
      try {
        console.log("🔄 Processing authentication callback...");

        // Handle the authentication callback
        const result = await handleAuthCallback();

        if (!mounted) return;

        if (result.success && result.user) {
          setStatus('success');
          setUserInfo(result.user);

          // Refresh the auth context
          await refreshAuth();

          // Get redirect URL from localStorage, query params, or default to home
          const storedRedirect = localStorage.getItem('auth_redirect_path');
          const redirectTo = storedRedirect || searchParams.get('redirect') || '/';

          // Clear stored redirect path
          if (storedRedirect) {
            localStorage.removeItem('auth_redirect_path');
          }

          // Redirect after a short delay to show success message
          setTimeout(() => {
            if (mounted) {
              router.push(redirectTo);
            }
          }, 2000);
        } else {
          setStatus('error');
          setError(result.error?.message || 'Authentication failed');
        }
      } catch (err) {
        console.error("❌ Authentication callback error:", err);
        if (mounted) {
          setStatus('error');
          setError("Authentication failed. Please try again.");
        }
      }
    }

    processCallback();

    return () => {
      mounted = false;
    };
  }, [router, searchParams, refreshAuth]);

  // Handle retry
  const handleRetry = () => {
    router.push('/signin');
  };

  // Handle manual redirect
  const handleGoHome = () => {
    router.push('/');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          {status === 'loading' && (
            <>
              <Loader2 className="mx-auto h-12 w-12 text-blue-600 animate-spin" />
              <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
                Processing Authentication
              </h2>
              <p className="mt-2 text-sm text-gray-600">
                Please wait while we complete your sign in...
              </p>
            </>
          )}

          {status === 'success' && (
            <>
              <CheckCircle className="mx-auto h-12 w-12 text-green-600" />
              <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
                Authentication Successful!
              </h2>
              <p className="mt-2 text-sm text-gray-600">
                Welcome back, {userInfo?.name || 'User'}! Redirecting you now...
              </p>
              <div className="mt-4">
                <Button onClick={handleGoHome} className="w-full">
                  Continue to Dashboard
                </Button>
              </div>
            </>
          )}

          {status === 'error' && (
            <>
              <XCircle className="mx-auto h-12 w-12 text-red-600" />
              <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
                Authentication Failed
              </h2>
              <p className="mt-2 text-sm text-gray-600">
                {error || 'Something went wrong during authentication.'}
              </p>
              <div className="mt-6 space-y-3">
                <Button onClick={handleRetry} className="w-full">
                  Try Again
                </Button>
                <Button onClick={handleGoHome} variant="outline" className="w-full">
                  Go to Home
                </Button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}

export default function AuthCallback() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <div className="mx-auto h-12 w-12 text-blue-600 animate-spin">
              <svg className="w-full h-full" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" className="opacity-25"></circle>
                <path fill="currentColor" className="opacity-75" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Processing Authentication
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              Please wait while we complete your sign in...
            </p>
          </div>
        </div>
      </div>
    }>
      <AuthCallbackContent />
    </Suspense>
  );
}