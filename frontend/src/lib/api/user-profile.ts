/**
 * User Profile API Service
 * 
 * Service for fetching user profile data from Lambda functions
 */

import { get, post } from '@/lib/api';

// Types matching the Lambda function responses
export interface UserProfile {
  userId: string;
  email: string;
  name?: string;
  stripeCustomerId?: string;
  plan: 'free' | 'professional' | 'enterprise';
  emailVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Subscription {
  subscriptionId: string;
  userId: string;
  stripeSubscriptionId: string;
  stripeCustomerId: string;
  status: SubscriptionStatus;
  planId: string;
  planName: string;
  priceId: string;
  currentPeriodStart: number;
  currentPeriodEnd: number;
  cancelAtPeriodEnd: boolean;
  trialEnd?: number;
  createdAt: string;
  updatedAt: string;
}

export type SubscriptionStatus = 
  | 'active'
  | 'canceled'
  | 'incomplete'
  | 'incomplete_expired'
  | 'past_due'
  | 'trialing'
  | 'unpaid';

export interface Usage {
  userId: string;
  date: string;
  searches: number;
  apiCalls: number;
  searchesRemaining: number;
  apiCallsRemaining: number;
}

export interface PlanLimits {
  searchesPerDay: number;
  apiCallsPerMonth: number;
  features: string[];
}

export interface UserProfileResponse {
  user: UserProfile;
  subscription?: Subscription;
  usage: Usage;
  planLimits: PlanLimits;
  searchUsagePercent: number;
  apiUsagePercent: number;
}

/**
 * Get complete user profile including subscription and usage data
 */
export async function getUserProfile(): Promise<UserProfileResponse> {
  try {
    const response = await get<UserProfileResponse>('/user/profile');
    return response;
  } catch (error) {
    console.error('Failed to fetch user profile:', error);
    throw error;
  }
}

/**
 * Get current subscription information
 */
export async function getCurrentSubscription(): Promise<Subscription | null> {
  try {
    const response = await get<Subscription>('/stripe/subscription');
    return response;
  } catch (error) {
    // If 404, user has no subscription
    if (error instanceof Error && error.message.includes('404')) {
      return null;
    }
    console.error('Failed to fetch subscription:', error);
    throw error;
  }
}

/**
 * Update user usage statistics
 */
export async function updateUsage(data: {
  searches?: number;
  apiCalls?: number;
  date?: string;
}): Promise<Usage> {
  try {
    const response = await post<Usage>('/user/usage', data);
    return response;
  } catch (error) {
    console.error('Failed to update usage:', error);
    throw error;
  }
}

/**
 * Cancel subscription
 */
export async function cancelSubscription(subscriptionId: string): Promise<void> {
  try {
    await post(`/stripe/subscription/${subscriptionId}/cancel`, {});
  } catch (error) {
    console.error('Failed to cancel subscription:', error);
    throw error;
  }
}

/**
 * Create Stripe checkout session
 */
export async function createCheckoutSession(data: {
  priceId: string;
  successUrl?: string;
  cancelUrl?: string;
}): Promise<{ sessionId: string; url: string }> {
  try {
    const response = await post<{ sessionId: string; url: string }>(
      '/stripe/create-checkout-session',
      data
    );
    return response;
  } catch (error) {
    console.error('Failed to create checkout session:', error);
    throw error;
  }
}

/**
 * Create Stripe customer portal session
 */
export async function createPortalSession(data: {
  customerId: string;
  returnUrl?: string;
}): Promise<{ url: string }> {
  try {
    const response = await post<{ url: string }>(
      '/stripe/create-portal-session',
      data
    );
    return response;
  } catch (error) {
    console.error('Failed to create portal session:', error);
    throw error;
  }
}

/**
 * Helper function to check if user has a specific feature
 */
export function hasFeature(plan: string, feature: string): boolean {
  const planFeatures: Record<string, string[]> = {
    free: [
      'basic_search',
      'community_api',
      'email_support',
      'basic_reporting',
    ],
    professional: [
      'basic_search',
      'community_api',
      'email_support',
      'basic_reporting',
      'advanced_search',
      'full_api',
      'priority_support',
      'advanced_analytics',
      'custom_dashboards',
      'threat_hunting',
    ],
    enterprise: [
      'basic_search',
      'community_api',
      'email_support',
      'basic_reporting',
      'advanced_search',
      'full_api',
      'priority_support',
      'advanced_analytics',
      'custom_dashboards',
      'threat_hunting',
      'unlimited_search',
      'premium_api',
      'phone_support',
      'custom_integrations',
      'dedicated_manager',
      'sla_guarantees',
    ],
  };

  return planFeatures[plan]?.includes(feature) || false;
}

/**
 * Helper function to get usage limits for a plan
 */
export function getUsageLimit(plan: string, resource: string): number {
  const limits: Record<string, Record<string, number>> = {
    searches_per_day: {
      free: 10,
      professional: 1000,
      enterprise: -1, // unlimited
    },
    api_calls_per_month: {
      free: 1000,
      professional: 50000,
      enterprise: -1, // unlimited
    },
  };

  return limits[resource]?.[plan] || 0;
}
