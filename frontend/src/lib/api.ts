import { fetchAuthSession } from "aws-amplify/auth";

// API base URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_ENDPOINT || "https://api.example.com";

// Get authentication headers
async function getAuthHeaders() {
  try {
    // Get current authentication session
    const session = await fetchAuthSession();

    // If there's an access token, add it to request headers
    if (session.tokens?.accessToken) {
      return {
        Authorization: `Bearer ${session.tokens.accessToken.toString()}`,
      };
    }

    return {};
  } catch (error) {
    console.error("Failed to get auth headers:", error);
    return {};
  }
}

// Generic API request function
export async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  try {
    // Get authentication headers
    const authHeaders = await getAuthHeaders();

    // Build complete URL
    const url = `${API_BASE_URL}${endpoint}`;

    // Merge request options
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      ...(authHeaders.Authorization ? { Authorization: authHeaders.Authorization } : {}),
      ...(options.headers as Record<string, string>),
    };

    const requestOptions: RequestInit = {
      ...options,
      headers,
    };

    // Send request
    const response = await fetch(url, requestOptions);

    // Check response status
    if (!response.ok) {
      // If authentication error, redirect to login page
      if (response.status === 401) {
        if (typeof window !== 'undefined') {
          window.location.href = "/signin?redirect=" + encodeURIComponent(window.location.pathname);
        }
        throw new Error("Authentication failed");
      }

      // Try to get error message from response
      let errorMessage = `API request failed: ${response.statusText}`;
      try {
        const errorData = await response.json();
        if (errorData.message) {
          errorMessage = errorData.message;
        }
      } catch {
        // Ignore JSON parsing errors
      }

      throw new Error(errorMessage);
    }

    // Parse response data
    const data = await response.json();
    return data as T;
  } catch (error) {
    console.error(`API request error for ${endpoint}:`, error);
    throw error;
  }
}

// GET请求
export async function get<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
  return apiRequest<T>(endpoint, { ...options, method: "GET" });
}

// POST请求
export async function post<T>(
  endpoint: string,
  data: unknown,
  options: RequestInit = {}
): Promise<T> {
  return apiRequest<T>(endpoint, {
    ...options,
    method: "POST",
    body: JSON.stringify(data),
  });
}

// PUT请求
export async function put<T>(
  endpoint: string,
  data: unknown,
  options: RequestInit = {}
): Promise<T> {
  return apiRequest<T>(endpoint, {
    ...options,
    method: "PUT",
    body: JSON.stringify(data),
  });
}

// DELETE请求
export async function del<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
  return apiRequest<T>(endpoint, { ...options, method: "DELETE" });
}