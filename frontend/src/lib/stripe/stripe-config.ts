/**
 * Stripe Configuration for SaaS Billing
 * 
 * This module handles Stripe integration for subscription management
 * compatible with Next.js static export and client-side authentication.
 */

import { loadStripe, Stripe } from '@stripe/stripe-js';

// Stripe configuration interface
interface StripeConfig {
  publishableKey: string;
  apiEndpoint: string;
}

// Global Stripe instance
let stripePromise: Promise<Stripe | null> | null = null;

/**
 * Get Stripe configuration from environment variables
 */
function getStripeConfig(): StripeConfig {
  const publishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
  const apiEndpoint = process.env.NEXT_PUBLIC_API_ENDPOINT;

  if (!publishableKey) {
    throw new Error('Missing NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY environment variable');
  }

  if (!apiEndpoint) {
    throw new Error('Missing NEXT_PUBLIC_API_ENDPOINT environment variable');
  }

  return {
    publishableKey,
    apiEndpoint,
  };
}

/**
 * Initialize and return Stripe instance
 */
export function getStripe(): Promise<Stripe | null> {
  if (!stripePromise) {
    const config = getStripeConfig();
    stripePromise = loadStripe(config.publishableKey);
  }
  return stripePromise;
}

/**
 * Stripe price IDs for different plans and billing cycles
 */
export const STRIPE_PRICE_IDS = {
  professional: {
    monthly: process.env.NEXT_PUBLIC_STRIPE_PROFESSIONAL_MONTHLY_PRICE_ID,
    annual: process.env.NEXT_PUBLIC_STRIPE_PROFESSIONAL_ANNUAL_PRICE_ID,
  },
  enterprise: {
    monthly: process.env.NEXT_PUBLIC_STRIPE_ENTERPRISE_MONTHLY_PRICE_ID,
    annual: process.env.NEXT_PUBLIC_STRIPE_ENTERPRISE_ANNUAL_PRICE_ID,
  },
} as const;

/**
 * Plan configuration for Stripe integration
 */
export interface StripePlan {
  id: string;
  name: string;
  priceIds: {
    monthly?: string;
    annual?: string;
  };
}

export const STRIPE_PLANS: StripePlan[] = [
  {
    id: 'professional',
    name: 'Professional',
    priceIds: {
      monthly: STRIPE_PRICE_IDS.professional.monthly,
      annual: STRIPE_PRICE_IDS.professional.annual,
    },
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    priceIds: {
      monthly: STRIPE_PRICE_IDS.enterprise.monthly,
      annual: STRIPE_PRICE_IDS.enterprise.annual,
    },
  },
];

// Checkout and portal session functions moved to /lib/api/user-profile.ts

// Subscription management functions moved to /lib/api/user-profile.ts
