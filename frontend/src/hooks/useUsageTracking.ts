/**
 * Usage Tracking Hook
 * 
 * Custom hook for tracking and managing user usage statistics
 */

"use client";

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { updateUsage, getUserProfile, type Usage, type UserProfileResponse } from '@/lib/api/user-profile';

export interface UseUsageTrackingReturn {
  usage: Usage | null;
  profileData: UserProfileResponse | null;
  isLoading: boolean;
  error: string | null;
  trackSearch: () => Promise<void>;
  trackApiCall: () => Promise<void>;
  refreshUsage: () => Promise<void>;
  canPerformSearch: boolean;
  canPerformApiCall: boolean;
  searchesRemaining: number;
  apiCallsRemaining: number;
  searchUsagePercent: number;
  apiUsagePercent: number;
}

/**
 * Hook for tracking user usage and checking limits
 */
export function useUsageTracking(): UseUsageTrackingReturn {
  const { isAuthenticated } = useAuth();
  const [usage, setUsage] = useState<Usage | null>(null);
  const [profileData, setProfileData] = useState<UserProfileResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Fetch current usage and profile data
   */
  const refreshUsage = useCallback(async () => {
    if (!isAuthenticated) {
      setUsage(null);
      setProfileData(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const data = await getUserProfile();
      setProfileData(data);
      setUsage(data.usage);
    } catch (err) {
      console.error('Failed to fetch usage data:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch usage data');
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated]);

  /**
   * Initialize usage data on mount and auth changes
   */
  useEffect(() => {
    refreshUsage();
  }, [refreshUsage]);

  /**
   * Track a search operation
   */
  const trackSearch = useCallback(async () => {
    if (!isAuthenticated) {
      throw new Error('Must be authenticated to track usage');
    }

    try {
      const updatedUsage = await updateUsage({ searches: 1 });
      setUsage(updatedUsage);
      
      // Update profile data with new usage
      if (profileData) {
        setProfileData({
          ...profileData,
          usage: updatedUsage,
          searchUsagePercent: profileData.planLimits.searchesPerDay === -1 
            ? 0 
            : Math.min(100, (updatedUsage.searches / profileData.planLimits.searchesPerDay) * 100),
        });
      }
    } catch (err) {
      console.error('Failed to track search:', err);
      throw err;
    }
  }, [isAuthenticated, profileData]);

  /**
   * Track an API call operation
   */
  const trackApiCall = useCallback(async () => {
    if (!isAuthenticated) {
      throw new Error('Must be authenticated to track usage');
    }

    try {
      const updatedUsage = await updateUsage({ apiCalls: 1 });
      setUsage(updatedUsage);
      
      // Update profile data with new usage
      if (profileData) {
        setProfileData({
          ...profileData,
          usage: updatedUsage,
          apiUsagePercent: profileData.planLimits.apiCallsPerMonth === -1 
            ? 0 
            : Math.min(100, (updatedUsage.apiCalls / profileData.planLimits.apiCallsPerMonth) * 100),
        });
      }
    } catch (err) {
      console.error('Failed to track API call:', err);
      throw err;
    }
  }, [isAuthenticated, profileData]);

  // Computed properties
  const canPerformSearch = !usage || usage.searchesRemaining > 0;
  const canPerformApiCall = !usage || usage.apiCallsRemaining > 0;
  const searchesRemaining = usage?.searchesRemaining || 0;
  const apiCallsRemaining = usage?.apiCallsRemaining || 0;
  const searchUsagePercent = profileData?.searchUsagePercent || 0;
  const apiUsagePercent = profileData?.apiUsagePercent || 0;

  return {
    usage,
    profileData,
    isLoading,
    error,
    trackSearch,
    trackApiCall,
    refreshUsage,
    canPerformSearch,
    canPerformApiCall,
    searchesRemaining,
    apiCallsRemaining,
    searchUsagePercent,
    apiUsagePercent,
  };
}

/**
 * Hook for checking if user can perform an action based on their plan
 */
export function useFeatureAccess() {
  const { profileData } = useUsageTracking();

  const hasFeature = useCallback((feature: string): boolean => {
    if (!profileData) return false;
    
    const plan = profileData.user.plan;
    
    // Free tier features
    const freeFeatures = [
      'basic_search',
      'community_api',
      'email_support',
      'basic_reporting',
    ];

    // Professional tier features
    const proFeatures = [
      ...freeFeatures,
      'advanced_search',
      'full_api',
      'priority_support',
      'advanced_analytics',
      'custom_dashboards',
      'threat_hunting',
    ];

    // Enterprise tier features
    const enterpriseFeatures = [
      ...proFeatures,
      'unlimited_search',
      'premium_api',
      'phone_support',
      'custom_integrations',
      'dedicated_manager',
      'sla_guarantees',
    ];

    if (plan === 'enterprise') {
      return enterpriseFeatures.includes(feature);
    }

    if (plan === 'professional') {
      return proFeatures.includes(feature);
    }

    return freeFeatures.includes(feature);
  }, [profileData]);

  const getUsageLimit = useCallback((resource: string): number => {
    if (!profileData) return 0;
    
    const limits: Record<string, Record<string, number>> = {
      searches_per_day: {
        free: 10,
        professional: 1000,
        enterprise: -1, // unlimited
      },
      api_calls_per_month: {
        free: 1000,
        professional: 50000,
        enterprise: -1, // unlimited
      },
    };

    const plan = profileData.user.plan;
    return limits[resource]?.[plan] || 0;
  }, [profileData]);

  return {
    hasFeature,
    getUsageLimit,
    profileData,
  };
}
