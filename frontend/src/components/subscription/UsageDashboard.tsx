"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { 
  Search, 
  Zap, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  Clock
} from "lucide-react";
import { useUsageTracking } from "@/hooks/useUsageTracking";

interface UsageDashboardProps {
  className?: string;
}

export function UsageDashboard({ className = "" }: UsageDashboardProps) {
  const {
    usage,
    profileData,
    isLoading,
    error,
    searchUsagePercent,
    apiUsagePercent,
    searchesRemaining,
    apiCallsRemaining,
  } = useUsageTracking();

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-gray-900"></div>
          <span className="ml-2">Loading usage data...</span>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="py-8">
          <div className="flex items-center text-red-600">
            <AlertTriangle className="w-5 h-5 mr-2" />
            <span>Error loading usage data: {error}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!profileData || !usage) {
    return (
      <Card className={className}>
        <CardContent className="py-8">
          <div className="text-center text-gray-600">
            No usage data available
          </div>
        </CardContent>
      </Card>
    );
  }

  const { user, planLimits } = profileData;
  const isUnlimited = (limit: number) => limit === -1;

  const getUsageStatus = (percent: number) => {
    if (percent >= 90) return { color: "text-red-600", icon: AlertTriangle, label: "Critical" };
    if (percent >= 75) return { color: "text-orange-600", icon: Clock, label: "Warning" };
    return { color: "text-green-600", icon: CheckCircle, label: "Good" };
  };

  const searchStatus = getUsageStatus(searchUsagePercent);
  const apiStatus = getUsageStatus(apiUsagePercent);

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="w-5 h-5" />
          Usage Dashboard
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Plan Information */}
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-semibold text-lg capitalize">{user.plan} Plan</h3>
            <p className="text-sm text-gray-600">Current billing period usage</p>
          </div>
          <Badge 
            variant={user.plan === 'enterprise' ? 'default' : user.plan === 'professional' ? 'secondary' : 'outline'}
            className="capitalize"
          >
            {user.plan}
          </Badge>
        </div>

        {/* Search Usage */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Search className="w-4 h-4 text-blue-600" />
              <span className="font-medium">Daily Searches</span>
            </div>
            <div className="flex items-center gap-2">
              <searchStatus.icon className={`w-4 h-4 ${searchStatus.color}`} />
              <span className={`text-sm ${searchStatus.color}`}>
                {searchStatus.label}
              </span>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>
                {usage.searches} / {isUnlimited(planLimits.searchesPerDay) ? '∞' : planLimits.searchesPerDay} searches
              </span>
              <span className="text-gray-600">
                {isUnlimited(planLimits.searchesPerDay) ? 'Unlimited' : `${searchesRemaining} remaining`}
              </span>
            </div>
            {!isUnlimited(planLimits.searchesPerDay) && (
              <Progress 
                value={searchUsagePercent} 
                className="h-2"
                // @ts-ignore - Progress component accepts custom color classes
                indicatorClassName={
                  searchUsagePercent >= 90 ? "bg-red-500" :
                  searchUsagePercent >= 75 ? "bg-orange-500" : "bg-green-500"
                }
              />
            )}
          </div>
        </div>

        {/* API Usage */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Zap className="w-4 h-4 text-purple-600" />
              <span className="font-medium">Monthly API Calls</span>
            </div>
            <div className="flex items-center gap-2">
              <apiStatus.icon className={`w-4 h-4 ${apiStatus.color}`} />
              <span className={`text-sm ${apiStatus.color}`}>
                {apiStatus.label}
              </span>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>
                {usage.apiCalls} / {isUnlimited(planLimits.apiCallsPerMonth) ? '∞' : planLimits.apiCallsPerMonth} calls
              </span>
              <span className="text-gray-600">
                {isUnlimited(planLimits.apiCallsPerMonth) ? 'Unlimited' : `${apiCallsRemaining} remaining`}
              </span>
            </div>
            {!isUnlimited(planLimits.apiCallsPerMonth) && (
              <Progress 
                value={apiUsagePercent} 
                className="h-2"
                // @ts-ignore - Progress component accepts custom color classes
                indicatorClassName={
                  apiUsagePercent >= 90 ? "bg-red-500" :
                  apiUsagePercent >= 75 ? "bg-orange-500" : "bg-green-500"
                }
              />
            )}
          </div>
        </div>

        {/* Usage Period */}
        <div className="pt-4 border-t">
          <div className="text-sm text-gray-600">
            <p>Usage period: {usage.date}</p>
            <p className="mt-1">
              Searches reset daily at midnight UTC. API calls reset monthly.
            </p>
          </div>
        </div>

        {/* Upgrade Prompt */}
        {(searchUsagePercent > 80 || apiUsagePercent > 80) && user.plan === 'free' && (
          <div className="pt-4 border-t">
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
              <div className="flex items-start">
                <AlertTriangle className="w-5 h-5 text-orange-600 mt-0.5 mr-3 flex-shrink-0" />
                <div>
                  <h4 className="font-medium text-orange-900">Approaching Usage Limits</h4>
                  <p className="text-sm text-orange-700 mt-1">
                    You're running low on your {searchUsagePercent > 80 ? 'daily searches' : 'monthly API calls'}. 
                    Consider upgrading to Professional for higher limits.
                  </p>
                  <a 
                    href="/plan" 
                    className="inline-block mt-2 text-sm font-medium text-orange-600 hover:text-orange-700"
                  >
                    View Plans →
                  </a>
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
