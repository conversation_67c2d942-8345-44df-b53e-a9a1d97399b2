import { User, Subscription, Usage } from '../types';
export declare class UserRepository {
    private tableName;
    getUser(userId: string): Promise<User | null>;
    createUser(user: Omit<User, 'createdAt' | 'updatedAt'>): Promise<User>;
    updateUser(userId: string, updates: Partial<User>): Promise<User>;
    getUserByEmail(email: string): Promise<User | null>;
    getUserByStripeCustomerId(stripeCustomerId: string): Promise<User | null>;
}
export declare class SubscriptionRepository {
    private tableName;
    getSubscription(userId: string, subscriptionId: string): Promise<Subscription | null>;
    getUserSubscriptions(userId: string): Promise<Subscription[]>;
    getActiveSubscription(userId: string): Promise<Subscription | null>;
    createSubscription(subscription: Omit<Subscription, 'createdAt' | 'updatedAt'>): Promise<Subscription>;
    updateSubscription(userId: string, subscriptionId: string, updates: Partial<Subscription>): Promise<Subscription>;
    getSubscriptionByStripeId(stripeSubscriptionId: string): Promise<Subscription | null>;
}
export declare class UsageRepository {
    private tableName;
    getUsage(userId: string, date: string): Promise<Usage | null>;
    updateUsage(userId: string, date: string, updates: Partial<Usage>): Promise<Usage>;
}
export declare const userRepository: UserRepository;
export declare const subscriptionRepository: SubscriptionRepository;
export declare const usageRepository: UsageRepository;
//# sourceMappingURL=dynamodb.d.ts.map