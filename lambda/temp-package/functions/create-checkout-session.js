"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const auth_1 = require("../utils/auth");
const dynamodb_1 = require("../utils/dynamodb");
const stripe_1 = require("../utils/stripe");
const handler = async (event) => {
    console.log('Create checkout session request:', JSON.stringify(event, null, 2));
    if (event.httpMethod === 'OPTIONS') {
        return (0, auth_1.handleCORS)();
    }
    try {
        const cognitoUser = (0, auth_1.getAuthenticatedUser)(event);
        const { priceId, successUrl, cancelUrl } = (0, auth_1.validateRequestBody)(event, ['priceId']);
        let user = await dynamodb_1.userRepository.getUser(cognitoUser.sub);
        if (!user) {
            user = await dynamodb_1.userRepository.createUser({
                userId: cognitoUser.sub,
                email: cognitoUser.email,
                name: cognitoUser.name,
                plan: 'free',
                emailVerified: cognitoUser.email_verified,
            });
        }
        let stripeCustomerId = user.stripeCustomerId;
        if (!stripeCustomerId) {
            const stripeCustomer = await (0, stripe_1.createStripeCustomer)(user.email, user.name, {
                userId: user.userId,
                email: user.email,
            });
            stripeCustomerId = stripeCustomer.id;
            await dynamodb_1.userRepository.updateUser(user.userId, {
                stripeCustomerId,
            });
        }
        const session = await (0, stripe_1.createCheckoutSession)(priceId, stripeCustomerId, successUrl, cancelUrl, {
            userId: user.userId,
            email: user.email,
            priceId,
        });
        return (0, auth_1.successResponse)({
            sessionId: session.id,
            url: session.url,
        });
    }
    catch (error) {
        console.error('Error creating checkout session:', error);
        if (error instanceof Error) {
            if (error.message.includes('No authorization token')) {
                return (0, auth_1.errorResponse)('Authentication required', 401);
            }
            if (error.message.includes('Invalid token') || error.message.includes('Token expired')) {
                return (0, auth_1.errorResponse)('Invalid or expired token', 401);
            }
            if (error.message.includes('Missing required field')) {
                return (0, auth_1.errorResponse)(error.message, 400);
            }
            return (0, auth_1.errorResponse)(error.message, 500);
        }
        return (0, auth_1.errorResponse)('Internal server error', 500);
    }
};
exports.handler = handler;
//# sourceMappingURL=create-checkout-session.js.map