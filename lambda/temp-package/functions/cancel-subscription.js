"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const auth_1 = require("../utils/auth");
const dynamodb_1 = require("../utils/dynamodb");
const stripe_1 = require("../utils/stripe");
const handler = async (event) => {
    console.log('Cancel subscription request:', JSON.stringify(event, null, 2));
    if (event.httpMethod === 'OPTIONS') {
        return (0, auth_1.handleCORS)();
    }
    try {
        const cognitoUser = (0, auth_1.getAuthenticatedUser)(event);
        const subscriptionId = event.pathParameters?.subscriptionId;
        if (!subscriptionId) {
            return (0, auth_1.errorResponse)('Subscription ID is required', 400);
        }
        const user = await dynamodb_1.userRepository.getUser(cognitoUser.sub);
        if (!user) {
            return (0, auth_1.errorResponse)('User not found', 404);
        }
        const subscription = await dynamodb_1.subscriptionRepository.getSubscription(user.userId, subscriptionId);
        if (!subscription) {
            return (0, auth_1.errorResponse)('Subscription not found', 404);
        }
        if (subscription.userId !== user.userId) {
            return (0, auth_1.errorResponse)('Unauthorized to cancel this subscription', 403);
        }
        if (subscription.status === 'canceled') {
            return (0, auth_1.errorResponse)('Subscription is already canceled', 400);
        }
        let cancelAtPeriodEnd = true;
        if (event.body) {
            try {
                const body = JSON.parse(event.body);
                if (typeof body.cancelAtPeriodEnd === 'boolean') {
                    cancelAtPeriodEnd = body.cancelAtPeriodEnd;
                }
            }
            catch (parseError) {
            }
        }
        const stripeSubscription = await (0, stripe_1.cancelStripeSubscription)(subscription.stripeSubscriptionId, cancelAtPeriodEnd);
        const updatedSubscription = await dynamodb_1.subscriptionRepository.updateSubscription(subscription.userId, subscription.subscriptionId, {
            status: stripeSubscription.status,
            cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end || false,
            currentPeriodEnd: stripeSubscription.current_period_end,
        });
        if (!cancelAtPeriodEnd || stripeSubscription.status === 'canceled') {
            await dynamodb_1.userRepository.updateUser(user.userId, {
                plan: 'free',
            });
        }
        return (0, auth_1.successResponse)({
            subscription: updatedSubscription,
            message: cancelAtPeriodEnd
                ? 'Subscription will be canceled at the end of the current billing period'
                : 'Subscription has been canceled immediately',
        });
    }
    catch (error) {
        console.error('Error canceling subscription:', error);
        if (error instanceof Error) {
            if (error.message.includes('No authorization token')) {
                return (0, auth_1.errorResponse)('Authentication required', 401);
            }
            if (error.message.includes('Invalid token') || error.message.includes('Token expired')) {
                return (0, auth_1.errorResponse)('Invalid or expired token', 401);
            }
            return (0, auth_1.errorResponse)(error.message, 500);
        }
        return (0, auth_1.errorResponse)('Internal server error', 500);
    }
};
exports.handler = handler;
//# sourceMappingURL=cancel-subscription.js.map