"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const auth_1 = require("../utils/auth");
const dynamodb_1 = require("../utils/dynamodb");
const types_1 = require("../types");
const handler = async (event) => {
    console.log('Get user profile request:', JSON.stringify(event, null, 2));
    if (event.httpMethod === 'OPTIONS') {
        return (0, auth_1.handleCORS)();
    }
    try {
        const cognitoUser = (0, auth_1.getAuthenticatedUser)(event);
        let user = await dynamodb_1.userRepository.getUser(cognitoUser.sub);
        if (!user) {
            user = await dynamodb_1.userRepository.createUser({
                userId: cognitoUser.sub,
                email: cognitoUser.email,
                name: cognitoUser.name,
                plan: 'free',
                emailVerified: cognitoUser.email_verified,
            });
        }
        const subscription = await dynamodb_1.subscriptionRepository.getActiveSubscription(user.userId);
        const today = new Date().toISOString().split('T')[0];
        const usage = await dynamodb_1.usageRepository.getUsage(user.userId, today);
        const planLimits = types_1.PLAN_LIMITS[user.plan] || types_1.PLAN_LIMITS.free;
        const searchUsagePercent = planLimits.searchesPerDay === -1
            ? 0
            : Math.min(100, ((usage?.searches || 0) / planLimits.searchesPerDay) * 100);
        const apiUsagePercent = planLimits.apiCallsPerMonth === -1
            ? 0
            : Math.min(100, ((usage?.apiCalls || 0) / planLimits.apiCallsPerMonth) * 100);
        const response = {
            user: {
                userId: user.userId,
                email: user.email,
                name: user.name,
                plan: user.plan,
                emailVerified: user.emailVerified,
                stripeCustomerId: user.stripeCustomerId,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt,
            },
            subscription: subscription ? {
                subscriptionId: subscription.subscriptionId,
                status: subscription.status,
                planId: subscription.planId,
                planName: subscription.planName,
                currentPeriodStart: subscription.currentPeriodStart,
                currentPeriodEnd: subscription.currentPeriodEnd,
                cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
                trialEnd: subscription.trialEnd,
            } : null,
            usage: {
                today: {
                    searches: usage?.searches || 0,
                    apiCalls: usage?.apiCalls || 0,
                    date: today,
                },
                limits: {
                    searchesPerDay: planLimits.searchesPerDay,
                    apiCallsPerMonth: planLimits.apiCallsPerMonth,
                },
                percentages: {
                    searches: searchUsagePercent,
                    apiCalls: apiUsagePercent,
                },
            },
            features: {
                available: planLimits.features,
                hasFeature: (feature) => planLimits.features.includes(feature),
            },
        };
        return (0, auth_1.successResponse)(response);
    }
    catch (error) {
        console.error('Error getting user profile:', error);
        if (error instanceof Error) {
            if (error.message.includes('No authorization token')) {
                return (0, auth_1.errorResponse)('Authentication required', 401);
            }
            if (error.message.includes('Invalid token') || error.message.includes('Token expired')) {
                return (0, auth_1.errorResponse)('Invalid or expired token', 401);
            }
            return (0, auth_1.errorResponse)(error.message, 500);
        }
        return (0, auth_1.errorResponse)('Internal server error', 500);
    }
};
exports.handler = handler;
//# sourceMappingURL=get-user-profile.js.map