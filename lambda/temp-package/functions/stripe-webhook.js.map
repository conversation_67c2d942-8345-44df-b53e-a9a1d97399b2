{"version": 3, "file": "stripe-webhook.js", "sourceRoot": "", "sources": ["../../src/functions/stripe-webhook.ts"], "names": [], "mappings": ";;;AAOA,wCAA+D;AAC/D,gDAA2E;AAC3E,4CAAgF;AAEzE,MAAM,OAAO,GAAG,KAAK,EAC1B,KAA2B,EACK,EAAE;IAClC,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE;QACtC,OAAO,EAAE,KAAK,CAAC,OAAO;QACtB,UAAU,EAAE,KAAK,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC;KACpC,CAAC,CAAC;IAEH,IAAI,CAAC;QAEH,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAEzF,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,IAAA,oBAAa,EAAC,0BAA0B,EAAE,GAAG,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAChB,OAAO,IAAA,oBAAa,EAAC,sBAAsB,EAAE,GAAG,CAAC,CAAC;QACpD,CAAC;QAGD,MAAM,WAAW,GAAG,IAAA,+BAAsB,EAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAElE,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE;YACtC,EAAE,EAAE,WAAW,CAAC,EAAE;YAClB,IAAI,EAAE,WAAW,CAAC,IAAI;SACvB,CAAC,CAAC;QAGH,QAAQ,WAAW,CAAC,IAAI,EAAE,CAAC;YACzB,KAAK,4BAA4B;gBAC/B,MAAM,8BAA8B,CAAC,WAAW,CAAC,IAAI,CAAC,MAAiC,CAAC,CAAC;gBACzF,MAAM;YAER,KAAK,+BAA+B;gBAClC,MAAM,yBAAyB,CAAC,WAAW,CAAC,IAAI,CAAC,MAA6B,CAAC,CAAC;gBAChF,MAAM;YAER,KAAK,+BAA+B;gBAClC,MAAM,yBAAyB,CAAC,WAAW,CAAC,IAAI,CAAC,MAA6B,CAAC,CAAC;gBAChF,MAAM;YAER,KAAK,+BAA+B;gBAClC,MAAM,yBAAyB,CAAC,WAAW,CAAC,IAAI,CAAC,MAA6B,CAAC,CAAC;gBAChF,MAAM;YAER,KAAK,2BAA2B;gBAC9B,MAAM,6BAA6B,CAAC,WAAW,CAAC,IAAI,CAAC,MAAwB,CAAC,CAAC;gBAC/E,MAAM;YAER,KAAK,wBAAwB;gBAC3B,MAAM,0BAA0B,CAAC,WAAW,CAAC,IAAI,CAAC,MAAwB,CAAC,CAAC;gBAC5E,MAAM;YAER;gBACE,OAAO,CAAC,GAAG,CAAC,yBAAyB,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;QAC7D,CAAC;QAED,OAAO,IAAA,sBAAe,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;IAE7C,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QAElD,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;YAC3B,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,2BAA2B,CAAC,EAAE,CAAC;gBACxD,OAAO,IAAA,oBAAa,EAAC,2BAA2B,EAAE,GAAG,CAAC,CAAC;YACzD,CAAC;YAED,OAAO,IAAA,oBAAa,EAAC,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QAC3C,CAAC;QAED,OAAO,IAAA,oBAAa,EAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;IACrD,CAAC;AACH,CAAC,CAAC;AAzEW,QAAA,OAAO,WAyElB;AAKF,KAAK,UAAU,8BAA8B,CAAC,OAAgC;IAC5E,OAAO,CAAC,GAAG,CAAC,wCAAwC,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC;IAElE,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,OAAO,CAAC,QAAkB,CAAC;QAC9C,MAAM,cAAc,GAAG,OAAO,CAAC,YAAsB,CAAC;QAEtD,IAAI,CAAC,UAAU,IAAI,CAAC,cAAc,EAAE,CAAC;YACnC,OAAO,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;YACxE,OAAO;QACT,CAAC;QAGD,MAAM,IAAI,GAAG,MAAM,yBAAc,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC;QAExE,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,UAAU,CAAC,CAAC;YAC7D,OAAO;QACT,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAC3D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;QACnE,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAKD,KAAK,UAAU,yBAAyB,CAAC,YAAiC;IACxE,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC;IAEjE,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,YAAY,CAAC,QAAkB,CAAC;QAGnD,MAAM,IAAI,GAAG,MAAM,yBAAc,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC;QAExE,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,UAAU,CAAC,CAAC;YAC7D,OAAO;QACT,CAAC;QAGD,MAAM,kBAAkB,GAAG,IAAA,8BAAqB,EAAC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAG5E,MAAM,iCAAsB,CAAC,kBAAkB,CAAC;YAC9C,GAAG,kBAAkB;YACrB,MAAM,EAAE,kBAAkB,CAAC,MAAa;SACzC,CAAC,CAAC;QAGH,MAAM,yBAAc,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE;YAC3C,IAAI,EAAE,kBAAkB,CAAC,MAAa;SACvC,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAC7D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;QAC7D,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAKD,KAAK,UAAU,yBAAyB,CAAC,YAAiC;IACxE,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC;IAEjE,IAAI,CAAC;QAEH,MAAM,oBAAoB,GAAG,MAAM,iCAAsB,CAAC,yBAAyB,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QAErG,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC1B,OAAO,CAAC,IAAI,CAAC,qCAAqC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC;YACrE,OAAO;QACT,CAAC;QAGD,MAAM,iCAAsB,CAAC,kBAAkB,CAC7C,oBAAoB,CAAC,MAAM,EAC3B,oBAAoB,CAAC,cAAc,EACnC;YACE,MAAM,EAAE,YAAY,CAAC,MAAa;YAClC,kBAAkB,EAAG,YAAoB,CAAC,oBAAoB;YAC9D,gBAAgB,EAAG,YAAoB,CAAC,kBAAkB;YAC1D,iBAAiB,EAAE,YAAY,CAAC,oBAAoB,IAAI,KAAK;YAC7D,QAAQ,EAAE,YAAY,CAAC,SAAS,IAAI,SAAS;SAC9C,CACF,CAAC;QAGF,IAAI,YAAY,CAAC,MAAM,KAAK,QAAQ,IAAI,YAAY,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;YAC3E,MAAM,kBAAkB,GAAG,IAAA,8BAAqB,EAAC,YAAY,EAAE,oBAAoB,CAAC,MAAM,CAAC,CAAC;YAC5F,MAAM,yBAAc,CAAC,UAAU,CAAC,oBAAoB,CAAC,MAAM,EAAE;gBAC3D,IAAI,EAAE,kBAAkB,CAAC,MAAa;aACvC,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,YAAY,CAAC,MAAM,KAAK,UAAU,IAAI,YAAY,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YAElF,MAAM,yBAAc,CAAC,UAAU,CAAC,oBAAoB,CAAC,MAAM,EAAE;gBAC3D,IAAI,EAAE,MAAM;aACb,CAAC,CAAC;QACL,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE,oBAAoB,CAAC,MAAM,CAAC,CAAC;IAC7E,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;QAC7D,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAKD,KAAK,UAAU,yBAAyB,CAAC,YAAiC;IACxE,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC;IAEjE,IAAI,CAAC;QAEH,MAAM,oBAAoB,GAAG,MAAM,iCAAsB,CAAC,yBAAyB,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QAErG,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC1B,OAAO,CAAC,IAAI,CAAC,qCAAqC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC;YACrE,OAAO;QACT,CAAC;QAGD,MAAM,iCAAsB,CAAC,kBAAkB,CAC7C,oBAAoB,CAAC,MAAM,EAC3B,oBAAoB,CAAC,cAAc,EACnC;YACE,MAAM,EAAE,UAAU;SACnB,CACF,CAAC;QAGF,MAAM,yBAAc,CAAC,UAAU,CAAC,oBAAoB,CAAC,MAAM,EAAE;YAC3D,IAAI,EAAE,MAAM;SACb,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE,oBAAoB,CAAC,MAAM,CAAC,CAAC;IAC7E,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;QAC7D,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAKD,KAAK,UAAU,6BAA6B,CAAC,OAAuB;IAClE,OAAO,CAAC,GAAG,CAAC,uCAAuC,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC;IAEjE,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,OAAO,CAAC,QAAkB,CAAC;QAG9C,MAAM,IAAI,GAAG,MAAM,yBAAc,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC;QAExE,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,UAAU,CAAC,CAAC;YAC7D,OAAO;QACT,CAAC;QAKD,OAAO,CAAC,GAAG,CAAC,qCAAqC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAClE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;QAClE,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAKD,KAAK,UAAU,0BAA0B,CAAC,OAAuB;IAC/D,OAAO,CAAC,GAAG,CAAC,oCAAoC,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC;IAE9D,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,OAAO,CAAC,QAAkB,CAAC;QAG9C,MAAM,IAAI,GAAG,MAAM,yBAAc,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC;QAExE,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,UAAU,CAAC,CAAC;YAC7D,OAAO;QACT,CAAC;QAKD,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAC/D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;QAC/D,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC"}