"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const auth_1 = require("../utils/auth");
const dynamodb_1 = require("../utils/dynamodb");
const stripe_1 = require("../utils/stripe");
const handler = async (event) => {
    console.log('Stripe webhook received:', {
        headers: event.headers,
        bodyLength: event.body?.length || 0,
    });
    try {
        const signature = event.headers['stripe-signature'] || event.headers['Stripe-Signature'];
        if (!signature) {
            return (0, auth_1.errorResponse)('Missing Stripe signature', 400);
        }
        if (!event.body) {
            return (0, auth_1.errorResponse)('Missing request body', 400);
        }
        const stripeEvent = (0, stripe_1.verifyWebhookSignature)(event.body, signature);
        console.log('Processing Stripe event:', {
            id: stripeEvent.id,
            type: stripeEvent.type,
        });
        switch (stripeEvent.type) {
            case 'checkout.session.completed':
                await handleCheckoutSessionCompleted(stripeEvent.data.object);
                break;
            case 'customer.subscription.created':
                await handleSubscriptionCreated(stripeEvent.data.object);
                break;
            case 'customer.subscription.updated':
                await handleSubscriptionUpdated(stripeEvent.data.object);
                break;
            case 'customer.subscription.deleted':
                await handleSubscriptionDeleted(stripeEvent.data.object);
                break;
            case 'invoice.payment_succeeded':
                await handleInvoicePaymentSucceeded(stripeEvent.data.object);
                break;
            case 'invoice.payment_failed':
                await handleInvoicePaymentFailed(stripeEvent.data.object);
                break;
            default:
                console.log(`Unhandled event type: ${stripeEvent.type}`);
        }
        return (0, auth_1.successResponse)({ received: true });
    }
    catch (error) {
        console.error('Error processing webhook:', error);
        if (error instanceof Error) {
            if (error.message.includes('Invalid webhook signature')) {
                return (0, auth_1.errorResponse)('Invalid webhook signature', 400);
            }
            return (0, auth_1.errorResponse)(error.message, 500);
        }
        return (0, auth_1.errorResponse)('Internal server error', 500);
    }
};
exports.handler = handler;
async function handleCheckoutSessionCompleted(session) {
    console.log('Processing checkout session completed:', session.id);
    try {
        const customerId = session.customer;
        const subscriptionId = session.subscription;
        if (!customerId || !subscriptionId) {
            console.warn('Missing customer or subscription ID in checkout session');
            return;
        }
        const user = await dynamodb_1.userRepository.getUserByStripeCustomerId(customerId);
        if (!user) {
            console.error('User not found for customer ID:', customerId);
            return;
        }
        console.log('Checkout completed for user:', user.userId);
    }
    catch (error) {
        console.error('Error handling checkout session completed:', error);
        throw error;
    }
}
async function handleSubscriptionCreated(subscription) {
    console.log('Processing subscription created:', subscription.id);
    try {
        const customerId = subscription.customer;
        const user = await dynamodb_1.userRepository.getUserByStripeCustomerId(customerId);
        if (!user) {
            console.error('User not found for customer ID:', customerId);
            return;
        }
        const mappedSubscription = (0, stripe_1.mapStripeSubscription)(subscription, user.userId);
        await dynamodb_1.subscriptionRepository.createSubscription({
            ...mappedSubscription,
            status: mappedSubscription.status
        });
        await dynamodb_1.userRepository.updateUser(user.userId, {
            plan: mappedSubscription.planId,
        });
        console.log('Subscription created for user:', user.userId);
    }
    catch (error) {
        console.error('Error handling subscription created:', error);
        throw error;
    }
}
async function handleSubscriptionUpdated(subscription) {
    console.log('Processing subscription updated:', subscription.id);
    try {
        const existingSubscription = await dynamodb_1.subscriptionRepository.getSubscriptionByStripeId(subscription.id);
        if (!existingSubscription) {
            console.warn('Subscription not found in database:', subscription.id);
            return;
        }
        await dynamodb_1.subscriptionRepository.updateSubscription(existingSubscription.userId, existingSubscription.subscriptionId, {
            status: subscription.status,
            currentPeriodStart: subscription.current_period_start,
            currentPeriodEnd: subscription.current_period_end,
            cancelAtPeriodEnd: subscription.cancel_at_period_end || false,
            trialEnd: subscription.trial_end || undefined,
        });
        if (subscription.status === 'active' || subscription.status === 'trialing') {
            const mappedSubscription = (0, stripe_1.mapStripeSubscription)(subscription, existingSubscription.userId);
            await dynamodb_1.userRepository.updateUser(existingSubscription.userId, {
                plan: mappedSubscription.planId,
            });
        }
        else if (subscription.status === 'canceled' || subscription.status === 'unpaid') {
            await dynamodb_1.userRepository.updateUser(existingSubscription.userId, {
                plan: 'free',
            });
        }
        console.log('Subscription updated for user:', existingSubscription.userId);
    }
    catch (error) {
        console.error('Error handling subscription updated:', error);
        throw error;
    }
}
async function handleSubscriptionDeleted(subscription) {
    console.log('Processing subscription deleted:', subscription.id);
    try {
        const existingSubscription = await dynamodb_1.subscriptionRepository.getSubscriptionByStripeId(subscription.id);
        if (!existingSubscription) {
            console.warn('Subscription not found in database:', subscription.id);
            return;
        }
        await dynamodb_1.subscriptionRepository.updateSubscription(existingSubscription.userId, existingSubscription.subscriptionId, {
            status: 'canceled',
        });
        await dynamodb_1.userRepository.updateUser(existingSubscription.userId, {
            plan: 'free',
        });
        console.log('Subscription deleted for user:', existingSubscription.userId);
    }
    catch (error) {
        console.error('Error handling subscription deleted:', error);
        throw error;
    }
}
async function handleInvoicePaymentSucceeded(invoice) {
    console.log('Processing invoice payment succeeded:', invoice.id);
    try {
        const customerId = invoice.customer;
        const user = await dynamodb_1.userRepository.getUserByStripeCustomerId(customerId);
        if (!user) {
            console.error('User not found for customer ID:', customerId);
            return;
        }
        console.log('Invoice payment succeeded for user:', user.userId);
    }
    catch (error) {
        console.error('Error handling invoice payment succeeded:', error);
        throw error;
    }
}
async function handleInvoicePaymentFailed(invoice) {
    console.log('Processing invoice payment failed:', invoice.id);
    try {
        const customerId = invoice.customer;
        const user = await dynamodb_1.userRepository.getUserByStripeCustomerId(customerId);
        if (!user) {
            console.error('User not found for customer ID:', customerId);
            return;
        }
        console.log('Invoice payment failed for user:', user.userId);
    }
    catch (error) {
        console.error('Error handling invoice payment failed:', error);
        throw error;
    }
}
//# sourceMappingURL=stripe-webhook.js.map