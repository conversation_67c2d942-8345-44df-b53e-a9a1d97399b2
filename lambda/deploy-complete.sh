#!/bin/bash

# Gcandle SaaS Lambda Functions - Complete Deployment with API Gateway
# This script deploys all Lambda functions with API Gateway

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
STACK_NAME="gcandle-saas-api"
STAGE=${1:-dev}
REGION=${2:-us-east-1}
BUCKET_NAME="gcandle-lambda-deployment-$(date +%s)"

echo -e "${BLUE}Gcandle SaaS Lambda Functions - Complete Deployment${NC}"
echo -e "${YELLOW}Stage: ${STAGE}${NC}"
echo -e "${YELLOW}Region: ${REGION}${NC}"
echo ""

# Check if .env file exists
if [ ! -f .env ]; then
    echo -e "${RED}Error: .env file not found${NC}"
    exit 1
fi

# Load environment variables
source .env

# Build TypeScript code
echo -e "${YELLOW}Building TypeScript code...${NC}"
npm run build
echo -e "${GREEN}Build completed successfully!${NC}"

# Check AWS credentials
echo -e "${YELLOW}Checking AWS credentials...${NC}"
aws sts get-caller-identity > /dev/null
echo -e "${GREEN}AWS credentials are valid${NC}"

# Create S3 bucket for deployment artifacts
echo -e "${YELLOW}Creating S3 bucket for deployment...${NC}"
aws s3 mb s3://${BUCKET_NAME} --region ${REGION}

# Package Lambda functions
echo -e "${YELLOW}Packaging Lambda functions...${NC}"
# Create a temporary directory for packaging
mkdir -p temp-package
cp -r dist/* temp-package/
cp -r node_modules temp-package/
cd temp-package
zip -r ../lambda-functions.zip . > /dev/null
cd ..
rm -rf temp-package

# Upload to S3
echo -e "${YELLOW}Uploading Lambda package to S3...${NC}"
aws s3 cp lambda-functions.zip s3://${BUCKET_NAME}/lambda-functions.zip

# Create complete CloudFormation template
echo -e "${YELLOW}Creating complete CloudFormation template...${NC}"
cat > complete-template.yaml << 'EOF'
AWSTemplateFormatVersion: '2010-09-09'
Description: Gcandle SaaS API Complete Stack

Parameters:
  Stage:
    Type: String
    Default: dev
  LambdaCodeBucket:
    Type: String
  LambdaCodeKey:
    Type: String
    Default: lambda-functions.zip
  StripeSecretKey:
    Type: String
    NoEcho: true
  StripeWebhookSecret:
    Type: String
    NoEcho: true
  CognitoUserPoolId:
    Type: String
  JwtSecret:
    Type: String
    NoEcho: true
  FrontendUrl:
    Type: String
  SesFromEmail:
    Type: String
    Description: Email address for sending notifications
  ApiUrl:
    Type: String
    Description: API Gateway URL

Resources:
  # IAM Role for Lambda Functions
  LambdaExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      Policies:
        - PolicyName: DynamoDBAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:Query
                  - dynamodb:Scan
                  - dynamodb:GetItem
                  - dynamodb:PutItem
                  - dynamodb:UpdateItem
                  - dynamodb:DeleteItem
                Resource:
                  - !GetAtt UsersTable.Arn
                  - !GetAtt SubscriptionsTable.Arn
                  - !GetAtt UsageTable.Arn
                  - !GetAtt InvoicesTable.Arn
                  - !Sub
                    - "${TableArn}/index/*"
                    - TableArn: !GetAtt UsersTable.Arn
                  - !Sub
                    - "${TableArn}/index/*"
                    - TableArn: !GetAtt SubscriptionsTable.Arn
                  - !Sub
                    - "${TableArn}/index/*"
                    - TableArn: !GetAtt UsageTable.Arn
                  - !Sub
                    - "${TableArn}/index/*"
                    - TableArn: !GetAtt InvoicesTable.Arn
        - PolicyName: SESAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ses:SendEmail
                  - ses:SendRawEmail
                Resource: '*'

  # API Gateway
  ApiGateway:
    Type: AWS::ApiGateway::RestApi
    Properties:
      Name: !Sub gcandle-saas-api-${Stage}
      Description: Gcandle SaaS API Gateway
      EndpointConfiguration:
        Types:
          - REGIONAL

  # API Gateway Deployment
  ApiGatewayDeployment:
    Type: AWS::ApiGateway::Deployment
    DependsOn:
      - CreateCheckoutSessionMethod
      - CreatePortalSessionMethod
      - GetSubscriptionMethod
      - CancelSubscriptionMethod
      - StripeWebhookMethod
      - GetUserProfileMethod
      - UpdateUsageMethod
      - CreateCheckoutSessionOptionsMethod
      - CreatePortalSessionOptionsMethod
      - GetSubscriptionOptionsMethod
      - CancelSubscriptionOptionsMethod
      - GetUserProfileOptionsMethod
      - UpdateUsageOptionsMethod
    Properties:
      RestApiId: !Ref ApiGateway
      StageName: !Ref Stage

  # Stripe API Resources
  StripeResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !GetAtt ApiGateway.RootResourceId
      PathPart: stripe

  # Create Checkout Session
  CreateCheckoutSessionResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !Ref StripeResource
      PathPart: create-checkout-session

  CreateCheckoutSessionMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref CreateCheckoutSessionResource
      HttpMethod: POST
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${CreateCheckoutSessionFunction.Arn}/invocations

  CreateCheckoutSessionOptionsMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref CreateCheckoutSessionResource
      HttpMethod: OPTIONS
      AuthorizationType: NONE
      Integration:
        Type: MOCK
        IntegrationResponses:
          - StatusCode: 200
            ResponseParameters:
              method.response.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
              method.response.header.Access-Control-Allow-Methods: "'POST,OPTIONS'"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        RequestTemplates:
          application/json: '{"statusCode": 200}'
      MethodResponses:
        - StatusCode: 200
          ResponseParameters:
            method.response.header.Access-Control-Allow-Headers: true
            method.response.header.Access-Control-Allow-Methods: true
            method.response.header.Access-Control-Allow-Origin: true

  # Create Portal Session
  CreatePortalSessionResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !Ref StripeResource
      PathPart: create-portal-session

  CreatePortalSessionMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref CreatePortalSessionResource
      HttpMethod: POST
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${CreatePortalSessionFunction.Arn}/invocations

  CreatePortalSessionOptionsMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref CreatePortalSessionResource
      HttpMethod: OPTIONS
      AuthorizationType: NONE
      Integration:
        Type: MOCK
        IntegrationResponses:
          - StatusCode: 200
            ResponseParameters:
              method.response.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
              method.response.header.Access-Control-Allow-Methods: "'POST,OPTIONS'"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        RequestTemplates:
          application/json: '{"statusCode": 200}'
      MethodResponses:
        - StatusCode: 200
          ResponseParameters:
            method.response.header.Access-Control-Allow-Headers: true
            method.response.header.Access-Control-Allow-Methods: true
            method.response.header.Access-Control-Allow-Origin: true

  # Get Subscription
  SubscriptionResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !Ref StripeResource
      PathPart: subscription

  GetSubscriptionMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref SubscriptionResource
      HttpMethod: GET
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${GetSubscriptionFunction.Arn}/invocations

  GetSubscriptionOptionsMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref SubscriptionResource
      HttpMethod: OPTIONS
      AuthorizationType: NONE
      Integration:
        Type: MOCK
        IntegrationResponses:
          - StatusCode: 200
            ResponseParameters:
              method.response.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
              method.response.header.Access-Control-Allow-Methods: "'GET,OPTIONS'"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        RequestTemplates:
          application/json: '{"statusCode": 200}'
      MethodResponses:
        - StatusCode: 200
          ResponseParameters:
            method.response.header.Access-Control-Allow-Headers: true
            method.response.header.Access-Control-Allow-Methods: true
            method.response.header.Access-Control-Allow-Origin: true

  # Cancel Subscription
  SubscriptionIdResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !Ref SubscriptionResource
      PathPart: '{subscriptionId}'

  CancelSubscriptionResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !Ref SubscriptionIdResource
      PathPart: cancel

  CancelSubscriptionMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref CancelSubscriptionResource
      HttpMethod: POST
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${CancelSubscriptionFunction.Arn}/invocations

  CancelSubscriptionOptionsMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref CancelSubscriptionResource
      HttpMethod: OPTIONS
      AuthorizationType: NONE
      Integration:
        Type: MOCK
        IntegrationResponses:
          - StatusCode: 200
            ResponseParameters:
              method.response.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
              method.response.header.Access-Control-Allow-Methods: "'POST,OPTIONS'"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        RequestTemplates:
          application/json: '{"statusCode": 200}'
      MethodResponses:
        - StatusCode: 200
          ResponseParameters:
            method.response.header.Access-Control-Allow-Headers: true
            method.response.header.Access-Control-Allow-Methods: true
            method.response.header.Access-Control-Allow-Origin: true

  # Stripe Webhook
  WebhookResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !Ref StripeResource
      PathPart: webhook

  StripeWebhookMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref WebhookResource
      HttpMethod: POST
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${StripeWebhookFunction.Arn}/invocations

  # User API Resources
  UserResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !GetAtt ApiGateway.RootResourceId
      PathPart: user

  # Get User Profile
  ProfileResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !Ref UserResource
      PathPart: profile

  GetUserProfileMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref ProfileResource
      HttpMethod: GET
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${GetUserProfileFunction.Arn}/invocations

  GetUserProfileOptionsMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref ProfileResource
      HttpMethod: OPTIONS
      AuthorizationType: NONE
      Integration:
        Type: MOCK
        IntegrationResponses:
          - StatusCode: 200
            ResponseParameters:
              method.response.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
              method.response.header.Access-Control-Allow-Methods: "'GET,OPTIONS'"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        RequestTemplates:
          application/json: '{"statusCode": 200}'
      MethodResponses:
        - StatusCode: 200
          ResponseParameters:
            method.response.header.Access-Control-Allow-Headers: true
            method.response.header.Access-Control-Allow-Methods: true
            method.response.header.Access-Control-Allow-Origin: true

  # Update Usage
  UsageResource:
    Type: AWS::ApiGateway::Resource
    Properties:
      RestApiId: !Ref ApiGateway
      ParentId: !Ref UserResource
      PathPart: usage

  UpdateUsageMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref UsageResource
      HttpMethod: POST
      AuthorizationType: NONE
      Integration:
        Type: AWS_PROXY
        IntegrationHttpMethod: POST
        Uri: !Sub arn:aws:apigateway:${AWS::Region}:lambda:path/2015-03-31/functions/${UpdateUsageFunction.Arn}/invocations

  UpdateUsageOptionsMethod:
    Type: AWS::ApiGateway::Method
    Properties:
      RestApiId: !Ref ApiGateway
      ResourceId: !Ref UsageResource
      HttpMethod: OPTIONS
      AuthorizationType: NONE
      Integration:
        Type: MOCK
        IntegrationResponses:
          - StatusCode: 200
            ResponseParameters:
              method.response.header.Access-Control-Allow-Headers: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
              method.response.header.Access-Control-Allow-Methods: "'POST,OPTIONS'"
              method.response.header.Access-Control-Allow-Origin: "'*'"
        RequestTemplates:
          application/json: '{"statusCode": 200}'
      MethodResponses:
        - StatusCode: 200
          ResponseParameters:
            method.response.header.Access-Control-Allow-Headers: true
            method.response.header.Access-Control-Allow-Methods: true
            method.response.header.Access-Control-Allow-Origin: true

  # Lambda Functions
  CreateCheckoutSessionFunction:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub gcandle-create-checkout-session-${Stage}
      Runtime: nodejs18.x
      Handler: functions/create-checkout-session.handler
      Code:
        S3Bucket: !Ref LambdaCodeBucket
        S3Key: !Ref LambdaCodeKey
      Role: !GetAtt LambdaExecutionRole.Arn
      Timeout: 30
      MemorySize: 256
      Environment:
        Variables:
          STAGE: !Ref Stage
          REGION: !Ref AWS::Region
          STRIPE_SECRET_KEY: !Ref StripeSecretKey
          STRIPE_WEBHOOK_SECRET: !Ref StripeWebhookSecret
          COGNITO_USER_POOL_ID: !Ref CognitoUserPoolId
          JWT_SECRET: !Ref JwtSecret
          FRONTEND_URL: !Ref FrontendUrl
          SES_FROM_EMAIL: !Ref SesFromEmail
          API_URL: !Ref ApiUrl
          DYNAMODB_USERS_TABLE: !Ref UsersTable
          DYNAMODB_SUBSCRIPTIONS_TABLE: !Ref SubscriptionsTable
          DYNAMODB_USAGE_TABLE: !Ref UsageTable
          DYNAMODB_INVOICES_TABLE: !Ref InvoicesTable

  CreatePortalSessionFunction:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub gcandle-create-portal-session-${Stage}
      Runtime: nodejs18.x
      Handler: functions/create-portal-session.handler
      Code:
        S3Bucket: !Ref LambdaCodeBucket
        S3Key: !Ref LambdaCodeKey
      Role: !GetAtt LambdaExecutionRole.Arn
      Timeout: 30
      MemorySize: 256
      Environment:
        Variables:
          STAGE: !Ref Stage
          REGION: !Ref AWS::Region
          STRIPE_SECRET_KEY: !Ref StripeSecretKey
          STRIPE_WEBHOOK_SECRET: !Ref StripeWebhookSecret
          COGNITO_USER_POOL_ID: !Ref CognitoUserPoolId
          JWT_SECRET: !Ref JwtSecret
          FRONTEND_URL: !Ref FrontendUrl
          SES_FROM_EMAIL: !Ref SesFromEmail
          API_URL: !Ref ApiUrl
          DYNAMODB_USERS_TABLE: !Ref UsersTable
          DYNAMODB_SUBSCRIPTIONS_TABLE: !Ref SubscriptionsTable
          DYNAMODB_USAGE_TABLE: !Ref UsageTable
          DYNAMODB_INVOICES_TABLE: !Ref InvoicesTable

  GetSubscriptionFunction:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub gcandle-get-subscription-${Stage}
      Runtime: nodejs18.x
      Handler: functions/get-subscription.handler
      Code:
        S3Bucket: !Ref LambdaCodeBucket
        S3Key: !Ref LambdaCodeKey
      Role: !GetAtt LambdaExecutionRole.Arn
      Timeout: 30
      MemorySize: 256
      Environment:
        Variables:
          STAGE: !Ref Stage
          REGION: !Ref AWS::Region
          STRIPE_SECRET_KEY: !Ref StripeSecretKey
          STRIPE_WEBHOOK_SECRET: !Ref StripeWebhookSecret
          COGNITO_USER_POOL_ID: !Ref CognitoUserPoolId
          JWT_SECRET: !Ref JwtSecret
          FRONTEND_URL: !Ref FrontendUrl
          SES_FROM_EMAIL: !Ref SesFromEmail
          API_URL: !Ref ApiUrl
          DYNAMODB_USERS_TABLE: !Ref UsersTable
          DYNAMODB_SUBSCRIPTIONS_TABLE: !Ref SubscriptionsTable
          DYNAMODB_USAGE_TABLE: !Ref UsageTable
          DYNAMODB_INVOICES_TABLE: !Ref InvoicesTable

  CancelSubscriptionFunction:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub gcandle-cancel-subscription-${Stage}
      Runtime: nodejs18.x
      Handler: functions/cancel-subscription.handler
      Code:
        S3Bucket: !Ref LambdaCodeBucket
        S3Key: !Ref LambdaCodeKey
      Role: !GetAtt LambdaExecutionRole.Arn
      Timeout: 30
      MemorySize: 256
      Environment:
        Variables:
          STAGE: !Ref Stage
          REGION: !Ref AWS::Region
          STRIPE_SECRET_KEY: !Ref StripeSecretKey
          STRIPE_WEBHOOK_SECRET: !Ref StripeWebhookSecret
          COGNITO_USER_POOL_ID: !Ref CognitoUserPoolId
          JWT_SECRET: !Ref JwtSecret
          FRONTEND_URL: !Ref FrontendUrl
          DYNAMODB_USERS_TABLE: !Ref UsersTable
          DYNAMODB_SUBSCRIPTIONS_TABLE: !Ref SubscriptionsTable
          DYNAMODB_USAGE_TABLE: !Ref UsageTable
          DYNAMODB_INVOICES_TABLE: !Ref InvoicesTable

  StripeWebhookFunction:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub gcandle-stripe-webhook-${Stage}
      Runtime: nodejs18.x
      Handler: functions/stripe-webhook.handler
      Code:
        S3Bucket: !Ref LambdaCodeBucket
        S3Key: !Ref LambdaCodeKey
      Role: !GetAtt LambdaExecutionRole.Arn
      Timeout: 30
      MemorySize: 256
      Environment:
        Variables:
          STAGE: !Ref Stage
          REGION: !Ref AWS::Region
          STRIPE_SECRET_KEY: !Ref StripeSecretKey
          STRIPE_WEBHOOK_SECRET: !Ref StripeWebhookSecret
          COGNITO_USER_POOL_ID: !Ref CognitoUserPoolId
          JWT_SECRET: !Ref JwtSecret
          FRONTEND_URL: !Ref FrontendUrl
          SES_FROM_EMAIL: !Ref SesFromEmail
          API_URL: !Ref ApiUrl
          DYNAMODB_USERS_TABLE: !Ref UsersTable
          DYNAMODB_SUBSCRIPTIONS_TABLE: !Ref SubscriptionsTable
          DYNAMODB_USAGE_TABLE: !Ref UsageTable
          DYNAMODB_INVOICES_TABLE: !Ref InvoicesTable

  GetUserProfileFunction:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub gcandle-get-user-profile-${Stage}
      Runtime: nodejs18.x
      Handler: functions/get-user-profile.handler
      Code:
        S3Bucket: !Ref LambdaCodeBucket
        S3Key: !Ref LambdaCodeKey
      Role: !GetAtt LambdaExecutionRole.Arn
      Timeout: 30
      MemorySize: 256
      Environment:
        Variables:
          STAGE: !Ref Stage
          REGION: !Ref AWS::Region
          STRIPE_SECRET_KEY: !Ref StripeSecretKey
          STRIPE_WEBHOOK_SECRET: !Ref StripeWebhookSecret
          COGNITO_USER_POOL_ID: !Ref CognitoUserPoolId
          JWT_SECRET: !Ref JwtSecret
          FRONTEND_URL: !Ref FrontendUrl
          SES_FROM_EMAIL: !Ref SesFromEmail
          API_URL: !Ref ApiUrl
          DYNAMODB_USERS_TABLE: !Ref UsersTable
          DYNAMODB_SUBSCRIPTIONS_TABLE: !Ref SubscriptionsTable
          DYNAMODB_USAGE_TABLE: !Ref UsageTable
          DYNAMODB_INVOICES_TABLE: !Ref InvoicesTable

  UpdateUsageFunction:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub gcandle-update-usage-${Stage}
      Runtime: nodejs18.x
      Handler: functions/update-usage.handler
      Code:
        S3Bucket: !Ref LambdaCodeBucket
        S3Key: !Ref LambdaCodeKey
      Role: !GetAtt LambdaExecutionRole.Arn
      Timeout: 30
      MemorySize: 256
      Environment:
        Variables:
          STAGE: !Ref Stage
          REGION: !Ref AWS::Region
          STRIPE_SECRET_KEY: !Ref StripeSecretKey
          STRIPE_WEBHOOK_SECRET: !Ref StripeWebhookSecret
          COGNITO_USER_POOL_ID: !Ref CognitoUserPoolId
          JWT_SECRET: !Ref JwtSecret
          FRONTEND_URL: !Ref FrontendUrl
          SES_FROM_EMAIL: !Ref SesFromEmail
          API_URL: !Ref ApiUrl
          DYNAMODB_USERS_TABLE: !Ref UsersTable
          DYNAMODB_SUBSCRIPTIONS_TABLE: !Ref SubscriptionsTable
          DYNAMODB_USAGE_TABLE: !Ref UsageTable
          DYNAMODB_INVOICES_TABLE: !Ref InvoicesTable

  # Lambda Permissions for API Gateway
  CreateCheckoutSessionPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref CreateCheckoutSessionFunction
      Action: lambda:InvokeFunction
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${ApiGateway}/*/*

  CreatePortalSessionPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref CreatePortalSessionFunction
      Action: lambda:InvokeFunction
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${ApiGateway}/*/*

  GetSubscriptionPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref GetSubscriptionFunction
      Action: lambda:InvokeFunction
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${ApiGateway}/*/*

  CancelSubscriptionPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref CancelSubscriptionFunction
      Action: lambda:InvokeFunction
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${ApiGateway}/*/*

  StripeWebhookPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref StripeWebhookFunction
      Action: lambda:InvokeFunction
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${ApiGateway}/*/*

  GetUserProfilePermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref GetUserProfileFunction
      Action: lambda:InvokeFunction
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${ApiGateway}/*/*

  UpdateUsagePermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref UpdateUsageFunction
      Action: lambda:InvokeFunction
      Principal: apigateway.amazonaws.com
      SourceArn: !Sub arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${ApiGateway}/*/*

  # DynamoDB Tables
  UsersTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub gcandle-users-${Stage}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: userId
          AttributeType: S
      KeySchema:
        - AttributeName: userId
          KeyType: HASH

  SubscriptionsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub gcandle-subscriptions-${Stage}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: userId
          AttributeType: S
        - AttributeName: subscriptionId
          AttributeType: S
      KeySchema:
        - AttributeName: userId
          KeyType: HASH
        - AttributeName: subscriptionId
          KeyType: RANGE

  UsageTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub gcandle-usage-${Stage}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: userId
          AttributeType: S
        - AttributeName: date
          AttributeType: S
      KeySchema:
        - AttributeName: userId
          KeyType: HASH
        - AttributeName: date
          KeyType: RANGE

  InvoicesTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub gcandle-invoices-${Stage}
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: invoiceId
          AttributeType: S
        - AttributeName: userId
          AttributeType: S
      KeySchema:
        - AttributeName: invoiceId
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: UserInvoicesIndex
          KeySchema:
            - AttributeName: userId
              KeyType: HASH
          Projection:
            ProjectionType: ALL

Outputs:
  ApiGatewayUrl:
    Description: API Gateway URL
    Value: !Sub https://${ApiGateway}.execute-api.${AWS::Region}.amazonaws.com/${Stage}
    Export:
      Name: !Sub ${AWS::StackName}-ApiUrl
  
  UsersTableName:
    Description: Users DynamoDB Table Name
    Value: !Ref UsersTable
    Export:
      Name: !Sub ${AWS::StackName}-UsersTable
EOF

# Deploy CloudFormation stack
echo -e "${YELLOW}Deploying complete CloudFormation stack...${NC}"
aws cloudformation deploy \
    --template-file complete-template.yaml \
    --stack-name "${STACK_NAME}-${STAGE}" \
    --capabilities CAPABILITY_IAM \
    --region "${REGION}" \
    --parameter-overrides \
        Stage="${STAGE}" \
        LambdaCodeBucket="${BUCKET_NAME}" \
        LambdaCodeKey="lambda-functions.zip" \
        StripeSecretKey="${STRIPE_SECRET_KEY}" \
        StripeWebhookSecret="${STRIPE_WEBHOOK_SECRET}" \
        CognitoUserPoolId="${COGNITO_USER_POOL_ID}" \
        JwtSecret="${JWT_SECRET}" \
        FrontendUrl="${FRONTEND_URL}" \
        SesFromEmail="${SES_FROM_EMAIL}" \
        ApiUrl="${API_URL}"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}Deployment completed successfully!${NC}"
    
    # Get API Gateway URL
    API_URL=$(aws cloudformation describe-stacks \
        --stack-name "${STACK_NAME}-${STAGE}" \
        --region "${REGION}" \
        --query 'Stacks[0].Outputs[?OutputKey==`ApiGatewayUrl`].OutputValue' \
        --output text)
    
    echo -e "${GREEN}API Gateway URL: ${API_URL}${NC}"
    echo ""
    echo -e "${YELLOW}Next steps:${NC}"
    echo -e "1. Update your .env file with API_URL=${API_URL}"
    echo -e "2. Configure Stripe webhooks to: ${API_URL}/stripe/webhook"
    echo -e "3. Test the API endpoints"
    
    # Clean up
    rm -f lambda-functions.zip complete-template.yaml
else
    echo -e "${RED}Deployment failed!${NC}"
    exit 1
fi
