/**
 * Authentication utilities for Lambda functions
 */

import jwksClient from 'jwks-rsa';
import jwt from 'jsonwebtoken';
import { APIGatewayProxyEvent } from 'aws-lambda';
import { getConfigInstance } from './config';
import { CognitoUser, JWTPayload } from '../types';

/**
 * Extract JWT token from Authorization header
 */
export function extractToken(event: APIGatewayProxyEvent): string | null {
  const authHeader = event.headers.Authorization || event.headers.authorization;
  
  if (!authHeader) {
    return null;
  }

  // Support both "Bearer token" and "token" formats
  if (authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  return authHeader;
}

/**
 * Verify and decode JWT token
 */
const client = jwksClient({
  jwksUri: `https://cognito-idp.${process.env.AWS_REGION}.amazonaws.com/${process.env.COGNITO_USER_POOL_ID}/.well-known/jwks.json`
});

function getKey(header: any, callback: any) {
  client.getSigningKey(header.kid, (err, key) => {
    const signingKey = key?.getPublicKey();
    callback(null, signingKey);
  });
}

export function verifyToken(token: string): JWTPayload {
  return new Promise((resolve, reject) => {
    jwt.verify(token, getKey, {
      algorithms: ['RS256'],
      issuer: `https://cognito-idp.${process.env.AWS_REGION}.amazonaws.com/${process.env.COGNITO_USER_POOL_ID}`,
    }, (err, decoded) => {
      if (err) {
        reject(new Error('Invalid token'));
      } else {
        resolve(decoded as JWTPayload);
      }
    });
  });
}

/**
 * Get authenticated user from request
 */
export function getAuthenticatedUser(event: APIGatewayProxyEvent): CognitoUser {
  const token = extractToken(event);
  
  if (!token) {
    throw new Error('No authorization token provided');
  }
  
  const payload = verifyToken(token);
  
  return {
    sub: payload.sub,
    email: payload.email,
    name: payload.name,
    email_verified: payload.email_verified || false,
  };
}

/**
 * Create response with CORS headers
 */
export function createResponse(
  statusCode: number,
  body: any,
  headers: Record<string, string> = {}
) {
  return {
    statusCode,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type,Authorization',
      'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
      ...headers,
    },
    body: JSON.stringify(body),
  };
}

/**
 * Create success response
 */
export function successResponse(data: any, statusCode: number = 200) {
  return createResponse(statusCode, {
    success: true,
    data,
  });
}

/**
 * Create error response
 */
export function errorResponse(
  error: string,
  statusCode: number = 400,
  details?: any
) {
  console.error('API Error:', error, details);
  
  return createResponse(statusCode, {
    success: false,
    error,
    ...(details && { details }),
  });
}

/**
 * Handle CORS preflight requests
 */
export function handleCORS() {
  return createResponse(200, {}, {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type,Authorization',
    'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
  });
}

/**
 * Validate request body
 */
export function validateRequestBody<T extends object>(
  event: APIGatewayProxyEvent,
  requiredFields: (keyof T)[]
): T {
  if (!event.body) {
    throw new Error('Request body is required');
  }

  let body: T;
  try {
    body = JSON.parse(event.body);
  } catch (error) {
    throw new Error('Invalid JSON in request body');
  }

  // Check required fields
  for (const field of requiredFields) {
    if (!(field in (body as object)) || (body as any)[field] === undefined || (body as any)[field] === null) {
      throw new Error(`Missing required field: ${String(field)}`);
    }
  }

  return body;
}

/**
 * Get user ID from authenticated request
 */
export function getUserId(event: APIGatewayProxyEvent): string {
  const user = getAuthenticatedUser(event);
  return user.sub;
}

/**
 * Check if user has permission for resource
 */
export function checkResourcePermission(
  requestedUserId: string,
  authenticatedUserId: string
): void {
  if (requestedUserId !== authenticatedUserId) {
    throw new Error('Insufficient permissions to access this resource');
  }
}
